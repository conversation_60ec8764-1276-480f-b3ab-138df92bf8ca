'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import { shouldShowCookieBanner, setCookieConsent } from '@/lib/cookie-consent'

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if cookie banner should be shown
    const shouldShow = shouldShowCookieBanner()

    if (shouldShow) {
      // Show the banner after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true)
        setIsLoading(false)
      }, 1000)

      return () => clearTimeout(timer)
    } else {
      setIsLoading(false)
    }
  }, [])

  const handleAccept = () => {
    setCookieConsent('accepted')
    setIsVisible(false)
  }

  const handleDecline = () => {
    setCookieConsent('declined')
    setIsVisible(false)

    // Optionally, you could disable analytics here
    // For now, we'll just record the preference
  }

  const handleClose = () => {
    setIsVisible(false)
    // Don't set consent status, just hide for this session
  }

  // Don't render anything while loading or if not visible
  if (isLoading || !isVisible) {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50" role="dialog" aria-labelledby="cookie-title" aria-describedby="cookie-description">
      <div className="max-w-lg mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 animate-in slide-in-from-bottom-4 duration-300">
          <div className="flex items-center gap-3 mb-3">
            <Cookie className="w-5 h-5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
            <p id="cookie-description" className="text-sm text-gray-700 dark:text-gray-300">
              This website uses cookies to improve your experience.
            </p>
            <button
              onClick={handleClose}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label="Close"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center gap-2 text-xs">
            <button
              onClick={handleAccept}
              className="px-3 py-1.5 bg-black hover:bg-gray-800 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
            >
              Agree
            </button>
            <Link
              href="/privacy-policy"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline underline-offset-2 transition-colors ml-auto"
            >
              Privacy and Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
