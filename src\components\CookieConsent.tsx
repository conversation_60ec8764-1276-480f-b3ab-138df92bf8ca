'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import { shouldShowCookieBanner, setCookieConsent } from '@/lib/cookie-consent'

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if cookie banner should be shown
    const shouldShow = shouldShowCookieBanner()

    if (shouldShow) {
      // Show the banner after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true)
        setIsLoading(false)
      }, 1000)

      return () => clearTimeout(timer)
    } else {
      setIsLoading(false)
    }
  }, [])

  const handleAccept = () => {
    setCookieConsent('accepted')
    setIsVisible(false)
  }

  const handleDecline = () => {
    setCookieConsent('declined')
    setIsVisible(false)

    // Optionally, you could disable analytics here
    // For now, we'll just record the preference
  }

  const handleClose = () => {
    setIsVisible(false)
    // Don't set consent status, just hide for this session
  }

  // Don't render anything while loading or if not visible
  if (isLoading || !isVisible) {
    return null
  }

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 pointer-events-none" />
      
      {/* Cookie Consent Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 md:p-6" role="dialog" aria-labelledby="cookie-title" aria-describedby="cookie-description">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 md:p-8 animate-in slide-in-from-bottom-4 duration-500">
            <div className="flex items-start gap-4">
              {/* Cookie Icon */}
              <div className="flex-shrink-0 mt-1">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                  <Cookie className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h3 id="cookie-title" className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  We value your privacy
                </h3>
                <p id="cookie-description" className="text-gray-700 dark:text-gray-300 text-sm md:text-base leading-relaxed mb-4">
                  This website uses cookies and similar technologies to enhance your browsing experience, 
                  provide personalized content, and analyze our traffic. We also share information about 
                  your use of our site with our analytics partners.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <button
                      onClick={handleAccept}
                      className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                    >
                      Accept All
                    </button>
                    <button
                      onClick={handleDecline}
                      className="px-6 py-2.5 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                    >
                      Decline
                    </button>
                  </div>

                  {/* Privacy Policy Link */}
                  <Link
                    href="/privacy-policy"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium underline underline-offset-2 transition-colors duration-200"
                  >
                    Privacy Policy
                  </Link>
                </div>
              </div>

              {/* Close Button */}
              <button
                onClick={handleClose}
                className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded"
                aria-label="Close cookie notice"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
